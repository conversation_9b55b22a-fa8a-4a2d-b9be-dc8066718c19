<template>
  <div class="banner-carousel">
    <div class="carousel-container">
      <div 
        class="carousel-slide"
        v-for="(slide, index) in slides"
        :key="index"
        :class="{ active: currentSlide === index }"
      >
        <img :src="slide.image" :alt="slide.alt" />
        <div class="slide-content">
          <img v-if="slide.textImage" :src="slide.textImage" :alt="slide.textAlt" class="slide-text" />
          <h2 v-if="slide.title" class="slide-title">{{ slide.title }}</h2>
          <p v-if="slide.description" class="slide-description">{{ slide.description }}</p>
        </div>
      </div>
    </div>
    
    <!-- 导航点 -->
    <div class="carousel-dots">
      <button
        v-for="(slide, index) in slides"
        :key="index"
        class="dot"
        :class="{ active: currentSlide === index }"
        @click="goToSlide(index)"
      ></button>
    </div>
    
    <!-- 导航箭头 -->
    <button class="carousel-arrow prev" @click="prevSlide">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
    <button class="carousel-arrow next" @click="nextSlide">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// Props
const props = defineProps({
  slides: {
    type: Array,
    default: () => [
      {
        image: '/static/media/banner1.f4ce7331.png',
        textImage: '/static/media/banner-text.7b1cb30a.png',
        alt: 'Banner 1',
        textAlt: 'Banner Text'
      },
      {
        image: '/static/media/banner2.4b5fbae7.png',
        title: '精彩游戏世界',
        description: '探索无限可能的游戏体验',
        alt: 'Banner 2'
      },
      {
        image: '/static/media/banner3.47070688.png',
        title: '加入我们',
        description: '与全球玩家一起畅游游戏世界',
        alt: 'Banner 3'
      }
    ]
  },
  autoPlay: {
    type: Boolean,
    default: true
  },
  interval: {
    type: Number,
    default: 5000
  }
})

// 响应式数据
const currentSlide = ref(0)
let autoPlayTimer = null

// 方法
const goToSlide = (index) => {
  currentSlide.value = index
}

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % props.slides.length
}

const prevSlide = () => {
  currentSlide.value = currentSlide.value === 0 ? props.slides.length - 1 : currentSlide.value - 1
}

const startAutoPlay = () => {
  if (props.autoPlay) {
    autoPlayTimer = setInterval(nextSlide, props.interval)
  }
}

const stopAutoPlay = () => {
  if (autoPlayTimer) {
    clearInterval(autoPlayTimer)
    autoPlayTimer = null
  }
}

// 生命周期
onMounted(() => {
  startAutoPlay()
})

onUnmounted(() => {
  stopAutoPlay()
})
</script>

<style scoped>
.banner-carousel {
  position: relative;
  width: 100%;
  height: 60vh;
  min-height: 400px;
  overflow: hidden;
  border-radius: 0 0 20px 20px;
}

.carousel-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.8s ease-in-out;
}

.carousel-slide.active {
  opacity: 1;
}

.carousel-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.slide-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 2;
  color: white;
}

.slide-text {
  max-width: 300px;
  height: auto;
  margin-bottom: 1rem;
}

.slide-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

.slide-description {
  font-size: 1.2rem;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
  max-width: 500px;
}

/* 导航点 */
.carousel-dots {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 3;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  background: transparent;
  cursor: pointer;
  transition: background 0.3s;
}

.dot.active {
  background: white;
}

/* 导航箭头 */
.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255,255,255,0.2);
  border: none;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s;
  z-index: 3;
}

.carousel-arrow:hover {
  background: rgba(255,255,255,0.3);
}

.carousel-arrow.prev {
  left: 20px;
}

.carousel-arrow.next {
  right: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .slide-title {
    font-size: 2rem;
  }
  
  .slide-description {
    font-size: 1rem;
  }
  
  .slide-text {
    max-width: 200px;
  }
  
  .carousel-arrow {
    width: 40px;
    height: 40px;
  }
  
  .carousel-arrow.prev {
    left: 10px;
  }
  
  .carousel-arrow.next {
    right: 10px;
  }
}
</style>
