<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const isLoading = ref(true)

// 生命周期
onMounted(() => {
  // 模拟加载完成
  setTimeout(() => {
    isLoading.value = false
  }, 1000)
})
</script>

<template>
  <div id="root">
    <!-- 加载器 -->
    <div v-if="isLoading" class="loader">
      <div class="loader-spinner"></div>
    </div>

    <!-- 主要内容 -->
    <div v-else class="page">
      <!-- 轮播区域 -->
      <div class="mv__item">
        <img src="/static/media/banner1.f4ce7331.png" alt="Banner" />
        <div class="banner-text-overlay">
          <img src="/static/media/banner-text.7b1cb30a.png" alt="Banner Text" />
        </div>
      </div>

      <!-- 游戏展示区域 -->
      <div class="pb180">
        <div class="game-container">
          <div class="game-grid">
            <div class="game-item">
              <img src="/static/media/img1.7e2a4ec0.png" alt="Game 1" />
            </div>
            <div class="game-item">
              <img src="/static/media/img2.d180cfb1.png" alt="Game 2" />
            </div>
            <div class="game-item">
              <img src="/static/media/img3.8bfff408.png" alt="Game 3" />
            </div>
            <div class="game-item">
              <img src="/static/media/img4.79e6e634.png" alt="Game 4" />
            </div>
            <div class="game-item">
              <img src="/static/media/img5.8ea6d61a.png" alt="Game 5" />
            </div>
            <div class="game-item">
              <img src="/static/media/img6.8a6f8ec0.png" alt="Game 6" />
            </div>
          </div>
        </div>
      </div>

      <!-- 联系区域 -->
      <div class="contact-section" :style="{ backgroundImage: 'url(/static/media/bg-contact.e5557380.jpeg)' }">
        <div class="contact-content">
          <div class="contact-icons">
            <div class="contact-icon">
              <img src="/static/media/email.85506f33.png" alt="Email" />
            </div>
            <div class="contact-icon">
              <img src="/static/media/address.e4a164c3.png" alt="Address" />
            </div>
            <div class="contact-icon">
              <img src="/static/media/reply.9e9c564a.png" alt="Reply" />
            </div>
          </div>
          <div class="message-image">
            <img src="/static/media/message-img.b1e61925.jpeg" alt="Message" />
          </div>
        </div>
      </div>

      <!-- 页脚 -->
      <footer class="footer">
        <div class="footer-content">
          <img src="/static/media/company.d598793a.png" alt="Company" />
        </div>
      </footer>
    </div>
  </div>
</template>

<style>
/* 基础样式重置 */
html {
  font-size: 100px;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 2;
  color: #333;
  background-color: #fff;
}

* {
  outline: none;
}

#root {
  width: 100%;
  height: 100%;
}

/* 加载器样式 */
.loader {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loader-spinner {
  width: 50px;
  height: 50px;
  border: 1px solid rgba(0,0,0,.08);
  border-left-color: rgba(0,0,0,.5);
  border-radius: 50%;
  animation: spinner .7s linear infinite;
}

@keyframes spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 页面样式 */
.page {
  width: 100%;
  min-height: 100vh;
}

/* 轮播区域样式 */
.mv__item {
  padding-bottom: 51%;
  overflow: hidden;
  position: relative;
  width: 100%;
}

.mv__item img {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-text-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.banner-text-overlay img {
  max-width: 300px;
  height: auto;
  width: auto;
}

/* 游戏区域样式 */
.pb180 {
  padding-bottom: 1.8rem;
  background: #fff;
}

.game-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.game-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  width: 100%;
}

.game-item {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  transition: transform 0.3s ease;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.game-item:hover {
  transform: scale(1.05);
}

.game-item img {
  width: 100%;
  height: auto;
  display: block;
}

/* 联系区域样式 */
.contact-section {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 60px 20px;
  position: relative;
  min-height: 300px;
}

.contact-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.contact-icons {
  display: flex;
  flex-direction: column;
  gap: 30px;
  align-items: flex-start;
}

.contact-icon {
  display: flex;
  align-items: center;
}

.contact-icon img {
  width: 60px;
  height: auto;
}

.message-image {
  flex-shrink: 0;
}

.message-image img {
  max-width: 400px;
  height: auto;
  border-radius: 8px;
}

/* 页脚样式 */
.footer {
  background: #333;
  padding: 20px 0;
  text-align: center;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}

.footer-content img {
  height: 40px;
  width: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .game-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .contact-content {
    flex-direction: column;
    gap: 30px;
    text-align: center;
  }

  .contact-icons {
    flex-direction: row;
    justify-content: space-around;
    width: 100%;
  }

  .banner-text-overlay img {
    max-width: 200px;
  }

  .message-image img {
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .game-grid {
    grid-template-columns: 1fr;
  }

  .contact-icons {
    flex-direction: column;
    align-items: center;
  }

  .banner-text-overlay img {
    max-width: 150px;
  }

  .message-image img {
    max-width: 250px;
  }
}
</style>
