<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const isLoading = ref(true)

// 生命周期
onMounted(() => {
  // 模拟加载完成
  setTimeout(() => {
    isLoading.value = false
  }, 1000)
})
</script>

<template>
  <div id="root">
    <!-- 加载器 -->
    <div v-if="isLoading" class="loader">
      <div class="loader-spinner"></div>
    </div>

    <!-- 主要内容 -->
    <div v-else>
      <!-- 轮播区域 -->
      <div class="mv__item">
        <img src="/static/media/banner1.f4ce7331.png" alt="Banner" />
        <div class="banner-text">
          <img src="/static/media/banner-text.7b1cb30a.png" alt="Banner Text" />
        </div>
      </div>

      <!-- 游戏展示区域 -->
      <div class="pb180">
        <div class="w100 h100">
          <div class="game-grid">
            <div class="game-item" v-for="i in 6" :key="i">
              <img :src="`/static/media/img${i}.png`" :alt="`Game ${i}`" />
            </div>
          </div>
        </div>
      </div>

      <!-- 联系区域 -->
      <div class="contact-section" :style="{ backgroundImage: 'url(/static/media/bg-contact.e5557380.jpeg)' }">
        <div class="contact-content">
          <div class="contact-info">
            <div class="contact-item">
              <img src="/static/media/email.85506f33.png" alt="Email" />
            </div>
            <div class="contact-item">
              <img src="/static/media/address.e4a164c3.png" alt="Address" />
            </div>
            <div class="contact-item">
              <img src="/static/media/reply.9e9c564a.png" alt="Reply" />
            </div>
          </div>
          <div class="message-section">
            <img src="/static/media/message-img.b1e61925.jpeg" alt="Message" />
          </div>
        </div>
      </div>

      <!-- 页脚 -->
      <footer class="footer">
        <div class="footer-content">
          <img src="/static/media/company.d598793a.png" alt="Company" />
        </div>
      </footer>
    </div>
  </div>
</template>

<style>
/* 导入原项目的CSS样式 */
@import url('/static/css/2.4d66736d.chunk.css');
@import url('/static/css/main.c10b2ef7.chunk.css');

/* 基础样式重置 */
html {
  font-size: 100px;
  --primary-color: #e57c58;
}

body {
  margin: 0;
  font-size: 12px;
  line-height: 2;
  color: #333;
  background-color: #fff;
}

* {
  outline: none;
}

#root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 加载器样式 */
.loader {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loader-spinner {
  content: "";
  display: block;
  width: 50px;
  height: 50px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -25px 0 0 -25px;
  font-size: 10px;
  border: 1px solid rgba(0,0,0,.08);
  border-left-color: rgba(0,0,0,.5);
  border-radius: 50%;
  animation: spinner .7s linear infinite;
}

@keyframes spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 轮播区域样式 */
.mv__item {
  padding-bottom: 51%;
  overflow: hidden;
  position: relative;
}

.mv__item img {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.banner-text img {
  max-width: 300px;
  height: auto;
}

/* 工具类样式 */
.pb180 {
  padding-bottom: 1.8rem;
}

.w100 {
  width: 100%;
}

.h100 {
  height: 100%;
}

/* 游戏网格样式 */
.game-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.game-item {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.game-item:hover {
  transform: scale(1.05);
}

.game-item img {
  width: 100%;
  height: auto;
  display: block;
}

/* 联系区域样式 */
.contact-section {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 60px 0;
  position: relative;
}

.contact-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.contact-item img {
  width: 60px;
  height: auto;
}

.message-section img {
  max-width: 400px;
  height: auto;
  border-radius: 8px;
}

/* 页脚样式 */
.footer {
  background: #333;
  padding: 20px 0;
  text-align: center;
}

.footer-content img {
  height: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .game-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 15px;
  }

  .contact-content {
    flex-direction: column;
    gap: 30px;
  }

  .contact-info {
    flex-direction: row;
    justify-content: space-around;
    width: 100%;
  }

  .banner-text img {
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .game-grid {
    grid-template-columns: 1fr;
  }

  .contact-info {
    flex-direction: column;
    align-items: center;
  }
}
</style>
