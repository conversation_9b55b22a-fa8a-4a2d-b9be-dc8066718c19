<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const isLoading = ref(true)

// 生命周期
onMounted(() => {
  // 模拟加载完成
  setTimeout(() => {
    isLoading.value = false
  }, 1000)
})
</script>

<template>
  <div id="root">
    <!-- 加载器 -->
    <div v-if="isLoading" class="loader"></div>

    <!-- 主要内容 -->
    <div v-else class="page">
      <!-- 轮播区域 -->
      <div class="mv__item">
        <img src="/static/media/banner1.f4ce7331.png" alt="Banner" />
        <div class="poa w-f h-f" style="display: flex; align-items: center; justify-content: center;">
          <img src="/static/media/banner-text.7b1cb30a.png" alt="Banner Text" style="max-width: 3rem; height: auto;" />
        </div>
      </div>

      <!-- 游戏展示区域 -->
      <div class="pb180">
        <div class="w-f h-f" style="display: flex; flex-wrap: wrap; justify-content: center; align-items: center; padding: 0.2rem;">
          <div style="width: 33.33%; padding: 0.1rem;">
            <img src="/static/media/img1.7e2a4ec0.png" alt="Game 1" class="w-f" />
          </div>
          <div style="width: 33.33%; padding: 0.1rem;">
            <img src="/static/media/img2.d180cfb1.png" alt="Game 2" class="w-f" />
          </div>
          <div style="width: 33.33%; padding: 0.1rem;">
            <img src="/static/media/img3.8bfff408.png" alt="Game 3" class="w-f" />
          </div>
          <div style="width: 33.33%; padding: 0.1rem;">
            <img src="/static/media/img4.79e6e634.png" alt="Game 4" class="w-f" />
          </div>
          <div style="width: 33.33%; padding: 0.1rem;">
            <img src="/static/media/img5.8ea6d61a.png" alt="Game 5" class="w-f" />
          </div>
          <div style="width: 33.33%; padding: 0.1rem;">
            <img src="/static/media/img6.8a6f8ec0.png" alt="Game 6" class="w-f" />
          </div>
        </div>
      </div>

      <!-- 联系区域 -->
      <div style="background-image: url(/static/media/bg-contact.e5557380.jpeg); background-size: cover; background-position: center; padding: 0.6rem 0; position: relative;">
        <div style="display: flex; justify-content: space-between; align-items: center; max-width: 12rem; margin: 0 auto; padding: 0 0.2rem;">
          <div style="display: flex; flex-direction: column; gap: 0.3rem;">
            <div>
              <img src="/static/media/email.85506f33.png" alt="Email" style="width: 0.6rem; height: auto;" />
            </div>
            <div>
              <img src="/static/media/address.e4a164c3.png" alt="Address" style="width: 0.6rem; height: auto;" />
            </div>
            <div>
              <img src="/static/media/reply.9e9c564a.png" alt="Reply" style="width: 0.6rem; height: auto;" />
            </div>
          </div>
          <div>
            <img src="/static/media/message-img.b1e61925.jpeg" alt="Message" style="max-width: 4rem; height: auto; border-radius: 0.08rem;" />
          </div>
        </div>
      </div>

      <!-- 页脚 -->
      <footer style="background: #333; padding: 0.2rem 0; text-align: center;">
        <div>
          <img src="/static/media/company.d598793a.png" alt="Company" style="height: 0.4rem; width: auto;" />
        </div>
      </footer>
    </div>
  </div>
</template>

<style>
/* 导入原项目的CSS样式 */
@import url('/static/css/2.4d66736d.chunk.css');
@import url('/static/css/main.c10b2ef7.chunk.css');

/* 基础样式重置 - 与原网站完全一致 */
html {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  -webkit-overflow-scrolling: touch;
  --primary-color: #e57c58;
  font-size: 100px;
}

body {
  margin: 0;
  font-size: 12px;
  line-height: 2;
  color: #333;
  background-color: #fff;
}

* {
  outline: none;
}

#root {
  width: 100%;
  height: 100%;
}

/* 加载器样式 - 与原网站完全一致 */
.loader {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
}

.loader:after {
  content: "";
  display: block;
  width: 50px;
  height: 50px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -25px 0 0 -25px;
  font-size: 10px;
  border: 1px solid rgba(0,0,0,.08);
  border-left-color: rgba(0,0,0,.5);
  border-radius: 50%;
  animation: spinner .7s linear infinite;
}

@keyframes spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 页面样式 */
.page {
  width: 100%;
  min-height: 100vh;
}

/* 轮播区域样式 - 与原网站完全一致 */
.mv__item {
  padding-bottom: 51%;
  overflow: hidden;
  position: relative;
}

.mv__item img {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

/* 游戏区域样式 - 与原网站完全一致 */
.pb180 {
  padding-bottom: 1.8rem;
}

/* 工具类样式 - 与原网站完全一致 */
.w-f {
  width: 100%;
}

.h-f {
  height: 100%;
}

.poa {
  position: absolute;
}

.poa-f {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mv__item img {
    object-fit: cover;
  }
}

@media (max-width: 480px) {
  .mv__item {
    padding-bottom: 60%;
  }
}
</style>
