<script setup>
import { ref, onMounted } from 'vue'
import BannerCarousel from './components/BannerCarousel.vue'

// 响应式数据
const isLoading = ref(true)

// 游戏数据
const games = ref([
  { id: 1, name: '冒险之旅', description: '探索神秘的世界，发现隐藏的宝藏', image: '/static/media/img1.7e2a4ec0.png' },
  { id: 2, name: '战略大师', description: '运用智慧和策略征服敌人', image: '/static/media/img2.d180cfb1.png' },
  { id: 3, name: '竞速狂飙', description: '体验极速驾驶的刺激感受', image: '/static/media/img3.8bfff408.png' },
  { id: 4, name: '魔法世界', description: '在魔法的世界中展开奇幻冒险', image: '/static/media/img4.79e6e634.png' },
  { id: 5, name: '太空探索', description: '探索浩瀚宇宙的无限奥秘', image: '/static/media/img5.8ea6d61a.png' },
  { id: 6, name: '古代传说', description: '重现古代英雄的传奇故事', image: '/static/media/img6.8a6f8ec0.png' }
])

// 生命周期
onMounted(() => {
  // 模拟加载完成
  setTimeout(() => {
    isLoading.value = false
  }, 1000)
})
</script>

<template>
  <div id="app">
    <!-- 加载器 -->
    <div v-if="isLoading" class="loader">
      <div class="loader-spinner"></div>
    </div>

    <!-- 主要内容 -->
    <div v-else class="main-content">
      <!-- 头部导航 -->
      <header class="header">
        <nav class="nav">
          <div class="nav-brand">
            <h1>Thisfun</h1>
          </div>
          <ul class="nav-menu">
            <li><a href="#home">首页</a></li>
            <li><a href="#games">游戏</a></li>
            <li><a href="#about">关于</a></li>
            <li><a href="#contact">联系</a></li>
          </ul>
        </nav>
      </header>

      <!-- 轮播横幅 -->
      <section class="banner-section">
        <BannerCarousel />
      </section>

      <!-- 游戏展示区 -->
      <section class="games-section">
        <div class="container">
          <h2 class="section-title">热门游戏</h2>
          <div class="games-grid">
            <div class="game-item" v-for="game in games" :key="game.id" @click="playGame(game)">
              <img :src="game.image" :alt="game.name" />
              <div class="game-info">
                <h3>{{ game.name }}</h3>
                <p>{{ game.description }}</p>
                <button class="play-btn">立即游戏</button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 联系我们 -->
      <section class="contact-section">
        <div class="container">
          <h2 class="section-title">联系我们</h2>
          <div class="contact-content">
            <div class="contact-info">
              <div class="contact-item">
                <img src="/static/media/email.85506f33.png" alt="Email" />
                <span><EMAIL></span>
              </div>
              <div class="contact-item">
                <img src="/static/media/address.e4a164c3.png" alt="Address" />
                <span>北京市朝阳区</span>
              </div>
            </div>
            <div class="contact-form">
              <form>
                <input type="text" placeholder="您的姓名" />
                <input type="email" placeholder="您的邮箱" />
                <textarea placeholder="留言内容"></textarea>
                <button type="submit">发送消息</button>
              </form>
            </div>
          </div>
        </div>
      </section>

      <!-- 页脚 -->
      <footer class="footer">
        <div class="container">
          <div class="footer-content">
            <div class="footer-logo">
              <img src="/static/media/company.d598793a.png" alt="Company" />
            </div>
            <div class="footer-text">
              <p>&copy; 2024 Thisfun. All rights reserved.</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </div>
</template>

<style scoped>
/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  outline: none;
}

#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #333;
  background-color: #fff;
}

/* 加载器样式 */
.loader {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loader-spinner {
  width: 50px;
  height: 50px;
  border: 1px solid rgba(0,0,0,.08);
  border-left-color: rgba(0,0,0,.5);
  border-radius: 50%;
  animation: spinner 0.7s linear infinite;
}

@keyframes spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 容器 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 头部导航 */
.header {
  background: #fff;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
}

.nav-brand h1 {
  color: #e57c58;
  font-size: 1.8rem;
  font-weight: bold;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-menu a:hover {
  color: #e57c58;
}

/* 主要内容 */
.main-content {
  padding-top: 80px; /* 为固定头部留出空间 */
}

/* 横幅区域 */
.banner-section {
  position: relative;
  height: 60vh;
  min-height: 400px;
  overflow: hidden;
}

.banner-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.banner-slide {
  position: relative;
  width: 100%;
  height: 100%;
}

.banner-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.banner-text img {
  max-width: 300px;
  height: auto;
}

/* 游戏展示区 */
.games-section {
  padding: 4rem 0;
  background: #f8f9fa;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 3rem;
  font-weight: 600;
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.game-item {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
}

.game-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.game-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.game-info {
  padding: 1.5rem;
}

.game-info h3 {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.game-info p {
  color: #666;
  line-height: 1.6;
}

/* 联系我们区域 */
.contact-section {
  padding: 4rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.contact-section .section-title {
  color: #fff;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-top: 2rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.contact-item img {
  width: 24px;
  height: 24px;
}

.contact-form {
  background: rgba(255,255,255,0.1);
  padding: 2rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.contact-form form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-form input,
.contact-form textarea {
  padding: 1rem;
  border: none;
  border-radius: 8px;
  background: rgba(255,255,255,0.9);
  color: #333;
  font-size: 1rem;
}

.contact-form textarea {
  min-height: 120px;
  resize: vertical;
}

.contact-form button {
  padding: 1rem 2rem;
  background: #e57c58;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s;
}

.contact-form button:hover {
  background: #d66a45;
}

/* 页脚 */
.footer {
  background: #333;
  color: #fff;
  padding: 2rem 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-logo img {
  height: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-menu {
    gap: 1rem;
  }

  .banner-text img {
    max-width: 200px;
  }

  .games-grid {
    grid-template-columns: 1fr;
  }

  .contact-content {
    grid-template-columns: 1fr;
  }

  .footer-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
</style>
